import { LegalDocument, type LegalDocumentMetadata } from '../domain/LegalDocument.js'
import type { DataRetentionInfo } from '../domain/DataDeletionRequest.js'

export class GetDataDeletionForm {
  execute(): LegalDocument {
    const metadata: LegalDocumentMetadata = {
      lastUpdated: new Date(),
      version: '1.0.0',
      language: 'es'
    }

    const content = this.generateDataDeletionFormHTML()

    return new LegalDocument(
      'data-deletion-form',
      'data-deletion',
      'BuscaFarma - Solicitud de Eliminación de Cuenta',
      content,
      metadata
    )
  }

  private generateDataDeletionFormHTML(): string {
    const dataRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Información Personal (nombre, email)',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de cuenta de usuario'
      },
      {
        dataType: 'Datos de Ubicación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Datos de Salud y Preferencias',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Información de salud del usuario'
      },
      {
        dataType: 'Medicamentos Favoritos',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Tokens de Autenticación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Tokens de seguridad'
      },
      {
        dataType: 'Registros de Auditoría',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento legal y prevención de fraude'
      },
      {
        dataType: 'Registro de Solicitud de Eliminación',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento con regulaciones de protección de datos'
      }
    ]

    return `
      <div class="data-deletion-form">
        <div class="app-branding">
          <h1>🏥 BuscaFarma</h1>
          <p class="app-description">Encuentra tu farmacia más cercana</p>
        </div>

        <div class="form-section">
          <h2>Solicitud de Eliminación de Cuenta</h2>
          <p class="intro-text">
            Entendemos que puedes querer eliminar tu cuenta de BuscaFarma. Esta página te permite 
            solicitar la eliminación de tu cuenta y todos los datos asociados de manera segura y transparente.
          </p>
        </div>

        <div class="form-section">
          <h3>📋 Instrucciones Paso a Paso</h3>
          <ol class="instructions-list">
            <li><strong>Completa el formulario:</strong> Proporciona tu email y confirma tu identidad</li>
            <li><strong>Revisión de datos:</strong> Revisa qué datos serán eliminados y cuáles se conservarán</li>
            <li><strong>Confirmación:</strong> Confirma tu solicitud de eliminación</li>
            <li><strong>Procesamiento:</strong> Procesaremos tu solicitud dentro de 30 días</li>
            <li><strong>Notificación:</strong> Te notificaremos cuando la eliminación esté completa</li>
          </ol>
        </div>

        <div class="form-section">
          <h3>🗂️ Información sobre Retención de Datos</h3>
          <p>La siguiente tabla muestra qué datos serán eliminados y cuáles pueden ser retenidos:</p>
          
          <div class="data-retention-table">
            <table>
              <thead>
                <tr>
                  <th>Tipo de Datos</th>
                  <th>¿Se Eliminará?</th>
                  <th>Período de Retención</th>
                  <th>Razón</th>
                </tr>
              </thead>
              <tbody>
                ${dataRetentionInfo.map(info => `
                  <tr class="${info.willBeDeleted ? 'will-delete' : 'will-retain'}">
                    <td>${info.dataType}</td>
                    <td>
                      <span class="status-badge ${info.willBeDeleted ? 'delete' : 'retain'}">
                        ${info.willBeDeleted ? '✅ Sí' : '❌ No'}
                      </span>
                    </td>
                    <td>${info.retentionPeriod}</td>
                    <td>${info.reason}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <div class="form-section">
          <h3>📝 Formulario de Solicitud</h3>
          <form id="deletionRequestForm" class="deletion-form">
            <div class="form-group">
              <label for="email">Email de la Cuenta:</label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                required 
                placeholder="<EMAIL>"
                class="form-input"
              >
              <small class="form-help">Ingresa el email asociado a tu cuenta de BuscaFarma</small>
            </div>

            <div class="form-group">
              <label for="reason">Razón de Eliminación (Opcional):</label>
              <select id="reason" name="reason" class="form-input">
                <option value="">Selecciona una razón (opcional)</option>
                <option value="no-longer-needed">Ya no necesito la aplicación</option>
                <option value="privacy-concerns">Preocupaciones de privacidad</option>
                <option value="switching-apps">Cambio a otra aplicación</option>
                <option value="account-issues">Problemas con la cuenta</option>
                <option value="other">Otra razón</option>
              </select>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmDeletion" name="confirmDeletion" required>
                <span class="checkmark"></span>
                Confirmo que entiendo que esta acción eliminará permanentemente mi cuenta y datos asociados, 
                y que algunos datos pueden ser retenidos por razones legales como se describe arriba.
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmIdentity" name="confirmIdentity" required>
                <span class="checkmark"></span>
                Confirmo que soy el propietario legítimo de esta cuenta y tengo autorización para solicitar su eliminación.
              </label>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn-primary" id="submitBtn">
                🗑️ Solicitar Eliminación de Cuenta
              </button>
            </div>
          </form>
        </div>

        <div class="form-section">
          <h3>📞 Soporte y Contacto</h3>
          <div class="contact-info">
            <p><strong>¿Necesitas ayuda con tu solicitud de eliminación?</strong></p>
            <ul>
              <li>📧 Email: <EMAIL></li>
              <li>⏰ Tiempo de respuesta: 24-48 horas</li>
              <li>🕒 Tiempo de procesamiento: Hasta 30 días</li>
            </ul>
            <p class="note">
              Si tienes problemas para acceder a tu cuenta o no puedes completar este formulario, 
              contacta nuestro equipo de soporte directamente.
            </p>
          </div>
        </div>

        <div class="form-section">
          <h3>⚖️ Información Legal</h3>
          <div class="legal-info">
            <p>
              Esta solicitud de eliminación se procesa de acuerdo con las regulaciones de protección de datos 
              aplicables, incluyendo GDPR y las políticas de Google Play Store. Algunos datos pueden ser 
              retenidos por períodos específicos para cumplir con obligaciones legales, prevención de fraude, 
              y seguridad.
            </p>
            <p>
              Para más información sobre cómo manejamos tus datos, consulta nuestra 
              <a href="/privacy-policy.html" target="_blank">Política de Privacidad</a>.
            </p>
          </div>
        </div>
      </div>

      <style>
        .data-deletion-form {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
        }

        .app-branding {
          text-align: center;
          margin-bottom: 40px;
          padding: 30px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px;
        }

        .app-branding h1 {
          margin: 0 0 10px 0;
          font-size: 2.5em;
          font-weight: bold;
        }

        .app-description {
          margin: 0;
          font-size: 1.2em;
          opacity: 0.9;
        }

        .form-section {
          margin-bottom: 40px;
          padding: 25px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-section h2 {
          color: #2c3e50;
          border-bottom: 3px solid #3498db;
          padding-bottom: 10px;
          margin-top: 0;
        }

        .form-section h3 {
          color: #34495e;
          margin-top: 0;
        }

        .instructions-list {
          padding-left: 20px;
        }

        .instructions-list li {
          margin-bottom: 10px;
        }

        .data-retention-table {
          overflow-x: auto;
          margin: 20px 0;
        }

        .data-retention-table table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }

        .data-retention-table th,
        .data-retention-table td {
          padding: 12px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }

        .data-retention-table th {
          background-color: #f8f9fa;
          font-weight: bold;
          color: #2c3e50;
        }

        .will-delete {
          background-color: #f8fff8;
        }

        .will-retain {
          background-color: #fff8f8;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.9em;
          font-weight: bold;
        }

        .status-badge.delete {
          background-color: #d4edda;
          color: #155724;
        }

        .status-badge.retain {
          background-color: #f8d7da;
          color: #721c24;
        }

        .deletion-form {
          max-width: 600px;
        }

        .form-group {
          margin-bottom: 25px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: bold;
          color: #2c3e50;
        }

        .form-input {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 6px;
          font-size: 16px;
          transition: border-color 0.3s;
        }

        .form-input:focus {
          outline: none;
          border-color: #3498db;
        }

        .form-help {
          display: block;
          margin-top: 5px;
          color: #666;
          font-size: 0.9em;
        }

        .checkbox-label {
          display: flex;
          align-items: flex-start;
          cursor: pointer;
          font-weight: normal;
        }

        .checkbox-label input[type="checkbox"] {
          margin-right: 10px;
          margin-top: 2px;
        }

        .form-actions {
          text-align: center;
          margin-top: 30px;
        }

        .btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 16px;
          font-weight: bold;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn-primary:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .contact-info ul {
          list-style: none;
          padding: 0;
        }

        .contact-info li {
          margin-bottom: 8px;
          padding: 8px;
          background: #f8f9fa;
          border-radius: 4px;
        }

        .note {
          background: #e3f2fd;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #2196f3;
          margin-top: 15px;
        }

        .legal-info {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 6px;
          border-left: 4px solid #6c757d;
        }

        .legal-info a {
          color: #3498db;
          text-decoration: none;
        }

        .legal-info a:hover {
          text-decoration: underline;
        }

        @media (max-width: 768px) {
          .data-deletion-form {
            padding: 10px;
          }
          
          .form-section {
            padding: 15px;
          }
          
          .data-retention-table {
            font-size: 0.9em;
          }
        }
      </style>

      <script>
        document.getElementById('deletionRequestForm').addEventListener('submit', async function(e) {
          e.preventDefault();
          
          const submitBtn = document.getElementById('submitBtn');
          const originalText = submitBtn.textContent;
          
          submitBtn.disabled = true;
          submitBtn.textContent = '⏳ Procesando...';
          
          const formData = new FormData(this);
          const data = {
            email: formData.get('email'),
            reason: formData.get('reason') || undefined,
            confirmDeletion: formData.get('confirmDeletion') === 'on',
            confirmIdentity: formData.get('confirmIdentity') === 'on'
          };
          
          try {
            const response = await fetch('/data-deletion/request', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
              alert('✅ Solicitud enviada exitosamente. Recibirás un email de confirmación con los detalles.');
              this.reset();
            } else {
              alert('❌ Error: ' + (result.message || 'No se pudo procesar la solicitud'));
            }
          } catch (error) {
            alert('❌ Error de conexión. Por favor intenta nuevamente.');
          } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
          }
        });
      </script>
    `
  }
}
