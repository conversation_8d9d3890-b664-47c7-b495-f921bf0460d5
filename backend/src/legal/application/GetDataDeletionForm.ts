import { LegalDocument, type LegalDocumentMetadata } from '../domain/LegalDocument.js'
import type { DataRetentionInfo } from '../domain/DataDeletionRequest.js'

export class GetDataDeletionForm {
  execute(): LegalDocument {
    const metadata: LegalDocumentMetadata = {
      lastUpdated: new Date(),
      version: '1.0.0',
      language: 'es'
    }

    const content = this.generateDataDeletionFormHTML()

    return new LegalDocument(
      'data-deletion-form',
      'data-deletion',
      'BuscaFarma - Solicitud de Eliminación de Cuenta',
      content,
      metadata
    )
  }

  /**
   * Generate a standalone HTML page for account deletion requests
   * This version includes full HTML structure for serving at root path
   */
  executeStandalone(): LegalDocument {
    const metadata: LegalDocumentMetadata = {
      lastUpdated: new Date(),
      version: '1.0.0',
      language: 'es'
    }

    const content = this.generateStandaloneDataDeletionHTML()

    return new LegalDocument(
      'data-deletion-standalone',
      'data-deletion',
      'BuscaFarma - Solicitud de Eliminación de Cuenta',
      content,
      metadata
    )
  }

  private generateDataDeletionFormHTML(): string {
    const dataRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Información Personal (nombre, email)',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de cuenta de usuario'
      },
      {
        dataType: 'Datos de Ubicación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Datos de Salud y Preferencias',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Información de salud del usuario'
      },
      {
        dataType: 'Medicamentos Favoritos',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Tokens de Autenticación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Tokens de seguridad'
      },
      {
        dataType: 'Registros de Auditoría',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento legal y prevención de fraude'
      },
      {
        dataType: 'Registro de Solicitud de Eliminación',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento con regulaciones de protección de datos'
      }
    ]

    return `
      <div class="data-deletion-form">
        <div class="app-branding">
          <h1>🏥 BuscaFarma</h1>
          <p class="app-description">Encuentra tu farmacia más cercana</p>
        </div>

        <div class="form-section">
          <h2>Solicitud de Eliminación de Cuenta</h2>
          <p class="intro-text">
            Entendemos que puedes querer eliminar tu cuenta de BuscaFarma. Esta página te permite 
            solicitar la eliminación de tu cuenta y todos los datos asociados de manera segura y transparente.
          </p>
        </div>

        <div class="form-section">
          <h3>📋 Instrucciones Paso a Paso</h3>
          <ol class="instructions-list">
            <li><strong>Completa el formulario:</strong> Proporciona tu email y confirma tu identidad</li>
            <li><strong>Revisión de datos:</strong> Revisa qué datos serán eliminados y cuáles se conservarán</li>
            <li><strong>Confirmación:</strong> Confirma tu solicitud de eliminación</li>
            <li><strong>Procesamiento:</strong> Procesaremos tu solicitud dentro de 30 días</li>
            <li><strong>Notificación:</strong> Te notificaremos cuando la eliminación esté completa</li>
          </ol>
        </div>

        <div class="form-section">
          <h3>🗂️ Información sobre Retención de Datos</h3>
          <p>La siguiente tabla muestra qué datos serán eliminados y cuáles pueden ser retenidos:</p>
          
          <div class="data-retention-table">
            <table>
              <thead>
                <tr>
                  <th>Tipo de Datos</th>
                  <th>¿Se Eliminará?</th>
                  <th>Período de Retención</th>
                  <th>Razón</th>
                </tr>
              </thead>
              <tbody>
                ${dataRetentionInfo.map(info => `
                  <tr class="${info.willBeDeleted ? 'will-delete' : 'will-retain'}">
                    <td>${info.dataType}</td>
                    <td>
                      <span class="status-badge ${info.willBeDeleted ? 'delete' : 'retain'}">
                        ${info.willBeDeleted ? '✅ Sí' : '❌ No'}
                      </span>
                    </td>
                    <td>${info.retentionPeriod}</td>
                    <td>${info.reason}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
          </div>
        </div>

        <div class="form-section">
          <h3>📝 Formulario de Solicitud</h3>
          <form id="deletionRequestForm" class="deletion-form">
            <div class="form-group">
              <label for="email">Email de la Cuenta:</label>
              <input 
                type="email" 
                id="email" 
                name="email" 
                required 
                placeholder="<EMAIL>"
                class="form-input"
              >
              <small class="form-help">Ingresa el email asociado a tu cuenta de BuscaFarma</small>
            </div>

            <div class="form-group">
              <label for="reason">Razón de Eliminación (Opcional):</label>
              <select id="reason" name="reason" class="form-input">
                <option value="">Selecciona una razón (opcional)</option>
                <option value="no-longer-needed">Ya no necesito la aplicación</option>
                <option value="privacy-concerns">Preocupaciones de privacidad</option>
                <option value="switching-apps">Cambio a otra aplicación</option>
                <option value="account-issues">Problemas con la cuenta</option>
                <option value="other">Otra razón</option>
              </select>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmDeletion" name="confirmDeletion" required>
                <span class="checkmark"></span>
                Confirmo que entiendo que esta acción eliminará permanentemente mi cuenta y datos asociados, 
                y que algunos datos pueden ser retenidos por razones legales como se describe arriba.
              </label>
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="confirmIdentity" name="confirmIdentity" required>
                <span class="checkmark"></span>
                Confirmo que soy el propietario legítimo de esta cuenta y tengo autorización para solicitar su eliminación.
              </label>
            </div>

            <div class="form-actions">
              <button type="submit" class="btn-primary" id="submitBtn">
                🗑️ Solicitar Eliminación de Cuenta
              </button>
            </div>
          </form>
        </div>

        <div class="form-section">
          <h3>📞 Soporte y Contacto</h3>
          <div class="contact-info">
            <p><strong>¿Necesitas ayuda con tu solicitud de eliminación?</strong></p>
            <ul>
              <li>📧 Email: <EMAIL></li>
              <li>⏰ Tiempo de respuesta: 24-48 horas</li>
              <li>🕒 Tiempo de procesamiento: Hasta 30 días</li>
            </ul>
            <p class="note">
              Si tienes problemas para acceder a tu cuenta o no puedes completar este formulario, 
              contacta nuestro equipo de soporte directamente.
            </p>
          </div>
        </div>

        <div class="form-section">
          <h3>⚖️ Información Legal</h3>
          <div class="legal-info">
            <p>
              Esta solicitud de eliminación se procesa de acuerdo con las regulaciones de protección de datos 
              aplicables, incluyendo GDPR y las políticas de Google Play Store. Algunos datos pueden ser 
              retenidos por períodos específicos para cumplir con obligaciones legales, prevención de fraude, 
              y seguridad.
            </p>
            <p>
              Para más información sobre cómo manejamos tus datos, consulta nuestra 
              <a href="/privacy-policy.html" target="_blank">Política de Privacidad</a>.
            </p>
          </div>
        </div>
      </div>

      <style>
        .data-deletion-form {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          line-height: 1.6;
          color: #333;
        }

        .app-branding {
          text-align: center;
          margin-bottom: 40px;
          padding: 30px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border-radius: 12px;
        }

        .app-branding h1 {
          margin: 0 0 10px 0;
          font-size: 2.5em;
          font-weight: bold;
        }

        .app-description {
          margin: 0;
          font-size: 1.2em;
          opacity: 0.9;
        }

        .form-section {
          margin-bottom: 40px;
          padding: 25px;
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .form-section h2 {
          color: #2c3e50;
          border-bottom: 3px solid #3498db;
          padding-bottom: 10px;
          margin-top: 0;
        }

        .form-section h3 {
          color: #34495e;
          margin-top: 0;
        }

        .instructions-list {
          padding-left: 20px;
        }

        .instructions-list li {
          margin-bottom: 10px;
        }

        .data-retention-table {
          overflow-x: auto;
          margin: 20px 0;
        }

        .data-retention-table table {
          width: 100%;
          border-collapse: collapse;
          margin: 20px 0;
        }

        .data-retention-table th,
        .data-retention-table td {
          padding: 12px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }

        .data-retention-table th {
          background-color: #f8f9fa;
          font-weight: bold;
          color: #2c3e50;
        }

        .will-delete {
          background-color: #f8fff8;
        }

        .will-retain {
          background-color: #fff8f8;
        }

        .status-badge {
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 0.9em;
          font-weight: bold;
        }

        .status-badge.delete {
          background-color: #d4edda;
          color: #155724;
        }

        .status-badge.retain {
          background-color: #f8d7da;
          color: #721c24;
        }

        .deletion-form {
          max-width: 600px;
        }

        .form-group {
          margin-bottom: 25px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: bold;
          color: #2c3e50;
        }

        .form-input {
          width: 100%;
          padding: 12px;
          border: 2px solid #ddd;
          border-radius: 6px;
          font-size: 16px;
          transition: border-color 0.3s;
        }

        .form-input:focus {
          outline: none;
          border-color: #3498db;
        }

        .form-help {
          display: block;
          margin-top: 5px;
          color: #666;
          font-size: 0.9em;
        }

        .checkbox-label {
          display: flex;
          align-items: flex-start;
          cursor: pointer;
          font-weight: normal;
        }

        .checkbox-label input[type="checkbox"] {
          margin-right: 10px;
          margin-top: 2px;
        }

        .form-actions {
          text-align: center;
          margin-top: 30px;
        }

        .btn-primary {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
          padding: 15px 30px;
          font-size: 16px;
          font-weight: bold;
          border-radius: 8px;
          cursor: pointer;
          transition: transform 0.2s, box-shadow 0.2s;
        }

        .btn-primary:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .contact-info ul {
          list-style: none;
          padding: 0;
        }

        .contact-info li {
          margin-bottom: 8px;
          padding: 8px;
          background: #f8f9fa;
          border-radius: 4px;
        }

        .note {
          background: #e3f2fd;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #2196f3;
          margin-top: 15px;
        }

        .legal-info {
          background: #f8f9fa;
          padding: 20px;
          border-radius: 6px;
          border-left: 4px solid #6c757d;
        }

        .legal-info a {
          color: #3498db;
          text-decoration: none;
        }

        .legal-info a:hover {
          text-decoration: underline;
        }

        @media (max-width: 768px) {
          .data-deletion-form {
            padding: 10px;
          }
          
          .form-section {
            padding: 15px;
          }
          
          .data-retention-table {
            font-size: 0.9em;
          }
        }
      </style>

      <script>
        document.getElementById('deletionRequestForm').addEventListener('submit', async function(e) {
          e.preventDefault();
          
          const submitBtn = document.getElementById('submitBtn');
          const originalText = submitBtn.textContent;
          
          submitBtn.disabled = true;
          submitBtn.textContent = '⏳ Procesando...';
          
          const formData = new FormData(this);
          const data = {
            email: formData.get('email'),
            reason: formData.get('reason') || undefined,
            confirmDeletion: formData.get('confirmDeletion') === 'on',
            confirmIdentity: formData.get('confirmIdentity') === 'on'
          };
          
          try {
            const response = await fetch('/data-deletion/request', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify(data)
            });
            
            const result = await response.json();
            
            if (response.ok) {
              alert('✅ Solicitud enviada exitosamente. Recibirás un email de confirmación con los detalles.');
              this.reset();
            } else {
              alert('❌ Error: ' + (result.message || 'No se pudo procesar la solicitud'));
            }
          } catch (error) {
            alert('❌ Error de conexión. Por favor intenta nuevamente.');
          } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
          }
        });
      </script>
    `
  }

  /**
   * Generate a complete standalone HTML page for account deletion requests
   * This includes full HTML structure with DOCTYPE, head, and body tags
   */
  private generateStandaloneDataDeletionHTML(): string {
    const dataRetentionInfo: DataRetentionInfo[] = [
      {
        dataType: 'Información Personal (nombre, email)',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de cuenta de usuario'
      },
      {
        dataType: 'Datos de Ubicación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Datos de Salud y Preferencias',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Información de salud del usuario'
      },
      {
        dataType: 'Medicamentos Favoritos',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Datos de preferencias de usuario'
      },
      {
        dataType: 'Tokens de Autenticación',
        willBeDeleted: true,
        retentionPeriod: 'Inmediato',
        reason: 'Tokens de seguridad'
      },
      {
        dataType: 'Registros de Auditoría',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento legal y prevención de fraude'
      },
      {
        dataType: 'Registro de Solicitud de Eliminación',
        willBeDeleted: false,
        retentionPeriod: '7 años',
        reason: 'Cumplimiento con regulaciones de protección de datos'
      }
    ]

    return `<!DOCTYPE html>
<html lang="es">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <meta name="description" content="Solicitud de eliminación de cuenta de BuscaFarma - Encuentra tu farmacia más cercana">
  <meta name="keywords" content="BuscaFarma, eliminación cuenta, privacidad, datos personales">
  <meta name="author" content="BuscaFarma">
  <title>BuscaFarma - Solicitud de Eliminación de Cuenta</title>

  <!-- Favicon -->
  <link rel="icon" type="image/x-icon" href="/favicon.ico">

  <!-- Open Graph Meta Tags -->
  <meta property="og:title" content="BuscaFarma - Solicitud de Eliminación de Cuenta">
  <meta property="og:description" content="Solicita la eliminación de tu cuenta de BuscaFarma de manera segura y transparente">
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://api.buscafarma.com/">

  <!-- Styles -->
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
      line-height: 1.6;
      color: #333;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      min-height: 100vh;
    }

    .container {
      max-width: 900px;
      margin: 0 auto;
      padding: 20px;
    }

    .header {
      text-align: center;
      margin-bottom: 40px;
      padding: 40px 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border-radius: 16px;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    }

    .header h1 {
      font-size: 3em;
      margin-bottom: 10px;
      font-weight: bold;
      text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .header .subtitle {
      font-size: 1.3em;
      opacity: 0.95;
      margin-bottom: 20px;
    }

    .header .description {
      font-size: 1.1em;
      opacity: 0.9;
      max-width: 600px;
      margin: 0 auto;
    }

    .content-section {
      background: white;
      margin-bottom: 30px;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
      border: 1px solid #e1e8ed;
    }

    .content-section h2 {
      color: #2c3e50;
      border-bottom: 3px solid #3498db;
      padding-bottom: 15px;
      margin-bottom: 25px;
      font-size: 1.8em;
    }

    .content-section h3 {
      color: #34495e;
      margin: 25px 0 15px 0;
      font-size: 1.4em;
    }

    .intro-text {
      font-size: 1.1em;
      color: #555;
      margin-bottom: 25px;
      line-height: 1.7;
    }

    .instructions-list {
      padding-left: 25px;
      margin: 20px 0;
    }

    .instructions-list li {
      margin-bottom: 15px;
      font-size: 1.05em;
      line-height: 1.6;
    }

    .instructions-list strong {
      color: #2c3e50;
    }

    .data-retention-table {
      overflow-x: auto;
      margin: 25px 0;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .data-retention-table table {
      width: 100%;
      border-collapse: collapse;
      background: white;
    }

    .data-retention-table th,
    .data-retention-table td {
      padding: 15px 12px;
      text-align: left;
      border-bottom: 1px solid #e1e8ed;
    }

    .data-retention-table th {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      font-weight: bold;
      color: #2c3e50;
      font-size: 1.05em;
    }

    .will-delete {
      background-color: #f8fff8;
    }

    .will-retain {
      background-color: #fff8f8;
    }

    .status-badge {
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 0.9em;
      font-weight: bold;
      display: inline-block;
    }

    .status-badge.delete {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .status-badge.retain {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .deletion-form {
      max-width: 600px;
      margin: 0 auto;
    }

    .form-group {
      margin-bottom: 25px;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 600;
      color: #2c3e50;
      font-size: 1.05em;
    }

    .form-input {
      width: 100%;
      padding: 15px;
      border: 2px solid #ddd;
      border-radius: 8px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: #fafafa;
    }

    .form-input:focus {
      outline: none;
      border-color: #3498db;
      background: white;
      box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .form-help {
      display: block;
      margin-top: 8px;
      color: #666;
      font-size: 0.95em;
      font-style: italic;
    }

    .checkbox-label {
      display: flex;
      align-items: flex-start;
      cursor: pointer;
      font-weight: normal;
      line-height: 1.6;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      border: 2px solid #e9ecef;
      transition: all 0.3s ease;
    }

    .checkbox-label:hover {
      background: #e9ecef;
      border-color: #3498db;
    }

    .checkbox-label input[type="checkbox"] {
      margin-right: 12px;
      margin-top: 2px;
      transform: scale(1.2);
    }

    .form-actions {
      text-align: center;
      margin-top: 40px;
    }

    .btn-primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
      padding: 18px 40px;
      font-size: 18px;
      font-weight: bold;
      border-radius: 50px;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .btn-primary:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    }

    .btn-primary:active {
      transform: translateY(-1px);
    }

    .btn-primary:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    .contact-info {
      background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
      padding: 25px;
      border-radius: 12px;
      border-left: 5px solid #2196f3;
    }

    .contact-info ul {
      list-style: none;
      padding: 0;
      margin: 15px 0;
    }

    .contact-info li {
      margin-bottom: 12px;
      padding: 12px 15px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      font-weight: 500;
    }

    .note {
      background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
      padding: 20px;
      border-radius: 10px;
      border-left: 5px solid #28a745;
      margin-top: 20px;
      font-style: italic;
    }

    .legal-info {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      padding: 25px;
      border-radius: 12px;
      border-left: 5px solid #6c757d;
      line-height: 1.7;
    }

    .legal-info a {
      color: #3498db;
      text-decoration: none;
      font-weight: 600;
    }

    .legal-info a:hover {
      text-decoration: underline;
    }

    .footer {
      text-align: center;
      margin-top: 50px;
      padding: 30px;
      background: #2c3e50;
      color: white;
      border-radius: 12px;
    }

    .footer p {
      margin-bottom: 10px;
    }

    .footer .copyright {
      font-size: 0.9em;
      opacity: 0.8;
    }

    /* Loading state */
    .loading {
      opacity: 0.7;
      pointer-events: none;
    }

    /* Success message */
    .success-message {
      background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
      color: #155724;
      padding: 20px;
      border-radius: 10px;
      border: 1px solid #c3e6cb;
      margin-top: 20px;
      display: none;
    }

    /* Error message */
    .error-message {
      background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
      color: #721c24;
      padding: 20px;
      border-radius: 10px;
      border: 1px solid #f5c6cb;
      margin-top: 20px;
      display: none;
    }

    /* Responsive Design */
    @media (max-width: 768px) {
      .container {
        padding: 10px;
      }

      .header h1 {
        font-size: 2.2em;
      }

      .header .subtitle {
        font-size: 1.1em;
      }

      .content-section {
        padding: 20px;
        margin-bottom: 20px;
      }

      .data-retention-table {
        font-size: 0.9em;
      }

      .form-input {
        padding: 12px;
      }

      .btn-primary {
        padding: 15px 30px;
        font-size: 16px;
      }
    }

    @media (max-width: 480px) {
      .header {
        padding: 25px 15px;
      }

      .header h1 {
        font-size: 1.8em;
      }

      .content-section {
        padding: 15px;
      }

      .data-retention-table th,
      .data-retention-table td {
        padding: 10px 8px;
        font-size: 0.85em;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header Section -->
    <div class="header">
      <h1>🏥 BuscaFarma</h1>
      <p class="subtitle">Encuentra tu farmacia más cercana</p>
      <p class="description">
        Solicitud de eliminación de cuenta y datos personales de forma segura y transparente
      </p>
    </div>

    <!-- Introduction Section -->
    <div class="content-section">
      <h2>📋 Solicitud de Eliminación de Cuenta</h2>
      <p class="intro-text">
        Entendemos que puedes querer eliminar tu cuenta de BuscaFarma. Esta página te permite
        solicitar la eliminación de tu cuenta y todos los datos asociados de manera segura y transparente,
        cumpliendo con las regulaciones de protección de datos y los requisitos de Google Play Store.
      </p>
      <p class="intro-text">
        <strong>Importante:</strong> Esta acción es irreversible. Una vez procesada tu solicitud,
        no podremos recuperar tu cuenta ni tus datos personales.
      </p>
    </div>

    <!-- Instructions Section -->
    <div class="content-section">
      <h3>📝 Instrucciones Paso a Paso</h3>
      <ol class="instructions-list">
        <li><strong>Completa el formulario:</strong> Proporciona tu email y confirma tu identidad</li>
        <li><strong>Revisión de datos:</strong> Revisa qué datos serán eliminados y cuáles se conservarán</li>
        <li><strong>Confirmación:</strong> Confirma tu solicitud de eliminación</li>
        <li><strong>Procesamiento:</strong> Procesaremos tu solicitud dentro de 30 días</li>
        <li><strong>Notificación:</strong> Te notificaremos cuando la eliminación esté completa</li>
      </ol>
    </div>

    <!-- Data Retention Information -->
    <div class="content-section">
      <h3>🗂️ Información sobre Retención de Datos</h3>
      <p>La siguiente tabla muestra qué datos serán eliminados y cuáles pueden ser retenidos:</p>

      <div class="data-retention-table">
        <table>
          <thead>
            <tr>
              <th>Tipo de Datos</th>
              <th>¿Se Eliminará?</th>
              <th>Período de Retención</th>
              <th>Razón</th>
            </tr>
          </thead>
          <tbody>
            ${dataRetentionInfo.map(info => `
              <tr class="${info.willBeDeleted ? 'will-delete' : 'will-retain'}">
                <td><strong>${info.dataType}</strong></td>
                <td>
                  <span class="status-badge ${info.willBeDeleted ? 'delete' : 'retain'}">
                    ${info.willBeDeleted ? '✅ Sí' : '❌ No'}
                  </span>
                </td>
                <td>${info.retentionPeriod}</td>
                <td>${info.reason}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Deletion Request Form -->
    <div class="content-section">
      <h3>📝 Formulario de Solicitud</h3>
      <form id="deletionRequestForm" class="deletion-form">
        <div class="form-group">
          <label for="email">Email de la Cuenta:</label>
          <input
            type="email"
            id="email"
            name="email"
            required
            placeholder="<EMAIL>"
            class="form-input"
          >
          <small class="form-help">Ingresa el email asociado a tu cuenta de BuscaFarma</small>
        </div>

        <div class="form-group">
          <label for="reason">Razón de Eliminación (Opcional):</label>
          <select id="reason" name="reason" class="form-input">
            <option value="">Selecciona una razón (opcional)</option>
            <option value="no-longer-needed">Ya no necesito la aplicación</option>
            <option value="privacy-concerns">Preocupaciones de privacidad</option>
            <option value="switching-apps">Cambio a otra aplicación</option>
            <option value="account-issues">Problemas con la cuenta</option>
            <option value="other">Otra razón</option>
          </select>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" id="confirmDeletion" name="confirmDeletion" required>
            Confirmo que entiendo que esta acción eliminará permanentemente mi cuenta y datos asociados,
            y que algunos datos pueden ser retenidos por razones legales como se describe arriba.
          </label>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input type="checkbox" id="confirmIdentity" name="confirmIdentity" required>
            Confirmo que soy el propietario legítimo de esta cuenta y tengo autorización para solicitar su eliminación.
          </label>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-primary" id="submitBtn">
            🗑️ Solicitar Eliminación de Cuenta
          </button>
        </div>

        <!-- Success Message -->
        <div id="successMessage" class="success-message">
          <strong>✅ Solicitud enviada exitosamente!</strong><br>
          Recibirás un email de confirmación con los detalles de tu solicitud.
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="error-message">
          <strong>❌ Error al procesar la solicitud</strong><br>
          <span id="errorText">Por favor intenta nuevamente.</span>
        </div>
      </form>
    </div>

    <!-- Support and Contact -->
    <div class="content-section">
      <h3>📞 Soporte y Contacto</h3>
      <div class="contact-info">
        <p><strong>¿Necesitas ayuda con tu solicitud de eliminación?</strong></p>
        <ul>
          <li>📧 Email: <EMAIL></li>
          <li>⏰ Tiempo de respuesta: 24-48 horas</li>
          <li>🕒 Tiempo de procesamiento: Hasta 30 días</li>
          <li>🌐 Sitio web: www.buscafarma.com</li>
        </ul>
        <p class="note">
          Si tienes problemas para acceder a tu cuenta o no puedes completar este formulario,
          contacta nuestro equipo de soporte directamente.
        </p>
      </div>
    </div>

    <!-- Legal Information -->
    <div class="content-section">
      <h3>⚖️ Información Legal</h3>
      <div class="legal-info">
        <p>
          Esta solicitud de eliminación se procesa de acuerdo con las regulaciones de protección de datos
          aplicables, incluyendo GDPR y las políticas de Google Play Store. Algunos datos pueden ser
          retenidos por períodos específicos para cumplir con obligaciones legales, prevención de fraude,
          y seguridad.
        </p>
        <p>
          Para más información sobre cómo manejamos tus datos, consulta nuestra
          <a href="/privacy-policy.html" target="_blank">Política de Privacidad</a>.
        </p>
        <p>
          <strong>BuscaFarma</strong> es una aplicación móvil que te ayuda a encontrar farmacias cercanas
          y gestionar tus medicamentos favoritos. No estamos afiliados con entidades gubernamentales.
        </p>
      </div>
    </div>

    <!-- Footer -->
    <div class="footer">
      <p><strong>🏥 BuscaFarma</strong></p>
      <p>Encuentra tu farmacia más cercana</p>
      <p class="copyright">© 2024 BuscaFarma. Todos los derechos reservados.</p>
    </div>
  </div>

  <!-- JavaScript -->
  <script>
    document.getElementById('deletionRequestForm').addEventListener('submit', async function(e) {
      e.preventDefault();

      const submitBtn = document.getElementById('submitBtn');
      const successMessage = document.getElementById('successMessage');
      const errorMessage = document.getElementById('errorMessage');
      const errorText = document.getElementById('errorText');
      const form = this;

      // Reset messages
      successMessage.style.display = 'none';
      errorMessage.style.display = 'none';

      // Show loading state
      const originalText = submitBtn.textContent;
      submitBtn.disabled = true;
      submitBtn.textContent = '⏳ Procesando...';
      form.classList.add('loading');

      const formData = new FormData(this);
      const data = {
        email: formData.get('email'),
        reason: formData.get('reason') || undefined,
        confirmDeletion: formData.get('confirmDeletion') === 'on',
        confirmIdentity: formData.get('confirmIdentity') === 'on'
      };

      try {
        const response = await fetch('/data-deletion/request', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data)
        });

        const result = await response.json();

        if (response.ok) {
          successMessage.style.display = 'block';
          form.reset();

          // Scroll to success message
          successMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
        } else {
          throw new Error(result.message || 'Error desconocido');
        }
      } catch (error) {
        errorText.textContent = error.message || 'Error de conexión. Por favor intenta nuevamente.';
        errorMessage.style.display = 'block';

        // Scroll to error message
        errorMessage.scrollIntoView({ behavior: 'smooth', block: 'center' });
      } finally {
        // Reset loading state
        submitBtn.disabled = false;
        submitBtn.textContent = originalText;
        form.classList.remove('loading');
      }
    });

    // Add smooth scrolling for internal links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (e) {
        e.preventDefault();
        const target = document.querySelector(this.getAttribute('href'));
        if (target) {
          target.scrollIntoView({ behavior: 'smooth' });
        }
      });
    });

    // Add form validation feedback
    const emailInput = document.getElementById('email');
    emailInput.addEventListener('blur', function() {
      if (this.value && !this.checkValidity()) {
        this.style.borderColor = '#e74c3c';
      } else {
        this.style.borderColor = '#ddd';
      }
    });

    emailInput.addEventListener('input', function() {
      if (this.checkValidity()) {
        this.style.borderColor = '#27ae60';
      }
    });
  </script>
</body>
</html>`
  }
}
