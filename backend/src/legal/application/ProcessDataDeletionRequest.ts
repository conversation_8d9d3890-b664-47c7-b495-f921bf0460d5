import { DataDeletionRequest } from '../domain/DataDeletionRequest.js'
import type { DataDeletionRepository } from '../infrastructure/repositories/DataDeletionRepository.js'
import type { EmailService } from '../../services/EmailService.js'
import { NotFoundError, ValidationError, ConflictError } from '../../server/errors/index.js'

export interface DeletionRequestInput {
  email: string
  reason?: string
  ipAddress?: string
  userAgent?: string
}

export interface UserData {
  id: string
  name: string
  email: string
}

export class ProcessDataDeletionRequest {
  constructor(
    private readonly dataDeletionRepository: DataDeletionRepository,
    private readonly emailService: EmailService
  ) {}

  async createRequest(input: DeletionRequestInput, userData?: UserData): Promise<DataDeletionRequest> {
    // Validate input
    if (!input.email || !this.isValidEmail(input.email)) {
      throw new ValidationError('Valid email address is required')
    }

    // If user data is provided, verify email matches
    if (userData && userData.email !== input.email) {
      throw new ValidationError('Email does not match authenticated user')
    }

    // Check for existing pending requests
    const existingRequest = await this.dataDeletionRepository.findPendingByEmail(input.email)
    if (existingRequest) {
      throw new ConflictError('A pending deletion request already exists for this email')
    }

    // Generate unique request ID
    const requestId = this.generateRequestId()

    // Create deletion request
    const deletionRequest = DataDeletionRequest.create(
      requestId,
      userData?.id || 'unknown',
      input.email,
      input.ipAddress,
      input.userAgent,
      input.reason
    )

    // Save to database
    const savedRequest = await this.dataDeletionRepository.create(deletionRequest)

    // Send confirmation email to user
    try {
      await this.emailService.sendDeletionRequestConfirmation(
        input.email,
        userData?.name || 'Usuario',
        requestId
      )
    } catch (error) {
      console.error('Failed to send confirmation email:', error)
      // Don't fail the request if email fails
    }

    // Send notification to admin (if configured)
    try {
      const adminEmail = process.env.ADMIN_EMAIL
      if (adminEmail) {
        await this.emailService.sendAdminDeletionNotification(
          adminEmail,
          input.email,
          requestId
        )
      }
    } catch (error) {
      console.error('Failed to send admin notification:', error)
      // Don't fail the request if email fails
    }

    return savedRequest
  }

  async getRequestStatus(requestId: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }
    return request
  }

  async getRequestsByEmail(email: string): Promise<DataDeletionRequest[]> {
    if (!this.isValidEmail(email)) {
      throw new ValidationError('Valid email address is required')
    }
    
    return this.dataDeletionRepository.findByEmail(email)
  }

  async processRequest(requestId: string, adminNotes?: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }

    if (!request.isProcessable()) {
      throw new ValidationError('Request is not in a processable state')
    }

    // Update status to processing
    const processingRequest = request.updateStatus('processing', adminNotes)
    const updatedRequest = await this.dataDeletionRepository.update(processingRequest)

    // Here you would implement the actual data deletion logic
    // For now, we'll simulate the process
    console.log(`Processing deletion request ${requestId} for user ${request.email}`)

    return updatedRequest
  }

  async completeRequest(requestId: string, adminNotes?: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }

    // Update status to completed
    const completedRequest = request.updateStatus('completed', adminNotes)
    const updatedRequest = await this.dataDeletionRepository.update(completedRequest)

    // Send completion notification to user
    try {
      await this.emailService.sendDeletionCompletedNotification(
        request.email,
        'Usuario', // We might not have the name anymore after deletion
        requestId
      )
    } catch (error) {
      console.error('Failed to send completion email:', error)
      // Don't fail the completion if email fails
    }

    return updatedRequest
  }

  async cancelRequest(requestId: string, adminNotes?: string): Promise<DataDeletionRequest> {
    const request = await this.dataDeletionRepository.findById(requestId)
    if (!request) {
      throw new NotFoundError('Deletion request not found')
    }

    const cancelledRequest = request.updateStatus('cancelled', adminNotes)
    return this.dataDeletionRepository.update(cancelledRequest)
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  private generateRequestId(): string {
    // Generate a unique request ID
    const timestamp = Date.now().toString(36)
    const random = Math.random().toString(36).substring(2, 8)
    return `del_${timestamp}_${random}`.toUpperCase()
  }

  // Method to simulate actual user data deletion
  // This would be implemented based on your specific data deletion requirements
  async executeUserDataDeletion(userId: string): Promise<void> {
    console.log(`Executing data deletion for user: ${userId}`)
    
    // TODO: Implement actual data deletion logic
    // This should include:
    // 1. Delete user record from database
    // 2. Delete medication favorites
    // 3. Invalidate authentication tokens
    // 4. Remove profile pictures from storage
    // 5. Anonymize or delete any other user-related data
    // 6. Keep audit logs and deletion request records for compliance
    
    // Example implementation:
    // await this.userRepository.deleteUser(userId)
    // await this.favoritesRepository.deleteByUserId(userId)
    // await this.authService.revokeAllTokens(userId)
    // await this.storageService.deleteUserFiles(userId)
  }
}
