import { Elysia, t } from 'elysia'
import { legalController, dataDeletionController } from '../server/dependencies.js'
import type { LegalDocumentType } from './domain/LegalDocument.js'

export const legalRouter = new Elysia()
  .get('/privacy-policy.html', async ({ set }) => {
    const result = await legalController.getPrivacyPolicy()

    // Set response headers for HTML content
    set.headers['Content-Type'] = 'text/html; charset=utf-8'
    set.headers['Cache-Control'] = 'public, max-age=3600' // Cache for 1 hour

    // Set status code
    set.status = result.status

    return result.data
  })
  .get('/privacy-policy.txt', async ({ set }) => {
    const result = await legalController.getPrivacyPolicyPlainText()

    // Set response headers for plain text content
    set.headers['Content-Type'] = 'text/plain; charset=utf-8'
    set.headers['Cache-Control'] = 'public, max-age=3600' // Cache for 1 hour

    // Set status code
    set.status = result.status

    return result.data
  })
  .get('/data-deletion.html', async ({ set }) => {
    const result = await legalController.getDataDeletionForm()

    // Set response headers for HTML content
    set.headers['Content-Type'] = 'text/html; charset=utf-8'
    set.headers['Cache-Control'] = 'public, max-age=3600' // Cache for 1 hour

    // Set status code
    set.status = result.status

    return result.data
  })
  .post('/data-deletion/request', async ({ body, headers, set, request }) => {
    const result = await dataDeletionController.createDeletionRequest({
      body,
      headers: headers as Record<string, string>,
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || undefined
    })

    set.status = result.status
    return result.data
  }, {
    body: t.Object({
      email: t.String({ format: 'email' }),
      reason: t.Optional(t.String()),
      confirmDeletion: t.Boolean(),
      confirmIdentity: t.Boolean()
    })
  })
  .get('/data-deletion/status/:requestId', async ({ params, set }) => {
    const result = await dataDeletionController.getRequestStatus(params.requestId)

    set.status = result.status
    return result.data
  })
  .get('/terms-of-service.txt', async ({ set }) => {
    const result = await legalController.getLegalDocumentPlainText('terms-of-service')

    // Set response headers for plain text content
    set.headers['Content-Type'] = 'text/plain; charset=utf-8'
    set.headers['Cache-Control'] = 'public, max-age=3600' // Cache for 1 hour

    // Set status code
    set.status = result.status

    return result.data
  })
  .get(
    '/terms-of-service.html',
    async ({ set }) => {
      const result = await legalController.getLegalDocument('terms-of-service')

      // Set response headers for HTML content
      set.headers['Content-Type'] = 'text/html; charset=utf-8'
      set.headers['Cache-Control'] = 'public, max-age=3600' // Cache for 1 hour

      // Set status code
      set.status = result.status

      return result.data
    },
    {
      detail: {
        tags: ['Legal'],
        summary: 'Get Privacy Policy',
        description: 'Returns the privacy policy as an HTML page. This endpoint is publicly accessible.',
        responses: {
          200: {
            description: 'Privacy policy HTML content',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: '<!DOCTYPE html><html>...</html>'
                }
              }
            }
          },
          500: {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'number', example: 500 },
                    message: { type: 'string', example: 'Internal server error' }
                  }
                }
              }
            }
          }
        }
      }
    }
  )
  .get(
    '/legal/:documentType',
    async ({ params, set }) => {
      const result = await legalController.getLegalDocument(params.documentType as LegalDocumentType)

      // Set response headers for HTML content
      set.headers['Content-Type'] = 'text/html; charset=utf-8'
      if (result.status === 200) {
        set.headers['Cache-Control'] = 'public, max-age=3600' // Cache for 1 hour
      }

      // Set status code
      set.status = result.status

      return result.data
    },
    {
      detail: {
        tags: ['Legal'],
        summary: 'Get Legal Document',
        description: 'Returns a legal document as an HTML page. This endpoint is publicly accessible.',
        parameters: [
          {
            name: 'documentType',
            in: 'path',
            required: true,
            schema: {
              type: 'string',
              enum: ['privacy-policy', 'terms-of-service', 'cookie-policy']
            },
            description: 'Type of legal document to retrieve'
          }
        ],
        responses: {
          200: {
            description: 'Legal document HTML content',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: '<!DOCTYPE html><html>...</html>'
                }
              }
            }
          },
          404: {
            description: 'Document not found',
            content: {
              'text/html': {
                schema: {
                  type: 'string',
                  example: '<!DOCTYPE html><html><body>Document not found</body></html>'
                }
              }
            }
          },
          500: {
            description: 'Internal server error',
            content: {
              'application/json': {
                schema: {
                  type: 'object',
                  properties: {
                    status: { type: 'number', example: 500 },
                    message: { type: 'string', example: 'Internal server error' }
                  }
                }
              }
            }
          }
        }
      }
    }
  )
