export type LegalDocumentType = 'privacy-policy' | 'terms-of-service' | 'cookie-policy'

export interface LegalDocumentMetadata {
  lastUpdated: Date
  version: string
  language: string
}

export class LegalDocument {
  constructor(
    public readonly id: string,
    public readonly type: LegalDocumentType,
    public readonly title: string,
    public readonly content: string,
    public readonly metadata: LegalDocumentMetadata
  ) {}

  toHTML(): string {
    return `<!DOCTYPE html>
<html lang="${this.metadata.language}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${this.title}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            border-bottom: 3px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            color: #34495e;
            margin-top: 30px;
        }
        .metadata {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
            font-size: 0.9em;
            color: #7f8c8d;
        }
        p {
            margin-bottom: 15px;
        }
        ul, ol {
            margin-bottom: 15px;
            padding-left: 30px;
        }
        li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>${this.title}</h1>
        <div class="metadata">
            <strong>Última actualización:</strong> ${this.metadata.lastUpdated.toLocaleDateString('es-ES')} | 
            <strong>Versión:</strong> ${this.metadata.version}
        </div>
        <div class="content">
            ${this.content}
        </div>
    </div>
</body>
</html>`
  }

  toPlainText(): string {
    // Convert HTML content to plain text for mobile consumption
    let plainText = this.content

    // Remove HTML tags and convert to readable format
    plainText = plainText
      // Remove div containers and styling
      .replace(/<div[^>]*>/g, '')
      .replace(/<\/div>/g, '\n')

      // Convert headers to uppercase with spacing
      .replace(/<h1[^>]*>(.*?)<\/h1>/g, `$1\n${'='.repeat(50)}\n`)
      .replace(/<h2[^>]*>(.*?)<\/h2>/g, `\n$1\n${'-'.repeat(30)}\n`)
      .replace(/<h3[^>]*>(.*?)<\/h3>/g, '\n$1\n')

      // Convert paragraphs
      .replace(/<p[^>]*>/g, '')
      .replace(/<\/p>/g, '\n\n')

      // Convert lists
      .replace(/<ul[^>]*>/g, '')
      .replace(/<\/ul>/g, '\n')
      .replace(/<ol[^>]*>/g, '')
      .replace(/<\/ol>/g, '\n')
      .replace(/<li[^>]*>/g, '• ')
      .replace(/<\/li>/g, '\n')

      // Convert strong/bold text
      .replace(/<strong[^>]*>(.*?)<\/strong>/g, '$1')
      .replace(/<b[^>]*>(.*?)<\/b>/g, '$1')

      // Remove any remaining HTML tags
      .replace(/<[^>]*>/g, '')

      // Clean up whitespace
      .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove excessive line breaks
      .replace(/^\s+|\s+$/g, '') // Trim start and end
      .replace(/[ \t]+/g, ' ') // Normalize spaces

    // Add document metadata at the top
    const formattedDate = this.metadata.lastUpdated.toLocaleDateString('es-ES')
    const header = `${this.title}\n${'='.repeat(this.title.length)}\n\nÚltima actualización: ${formattedDate}\nVersión: ${this.metadata.version}\n\n`

    return header + plainText
  }

  toJSON() {
    return {
      id: this.id,
      type: this.type,
      title: this.title,
      content: this.content,
      metadata: {
        ...this.metadata,
        lastUpdated: this.metadata.lastUpdated.toISOString()
      }
    }
  }
}
