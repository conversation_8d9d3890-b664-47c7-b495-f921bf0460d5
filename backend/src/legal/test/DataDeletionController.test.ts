import { describe, it, expect, beforeEach, jest } from 'bun:test'
import { DataDeletionController } from '../infrastructure/controllers/DataDeletionController.js'
import { ProcessDataDeletionRequest } from '../application/ProcessDataDeletionRequest.js'
import { DataDeletionRequest } from '../domain/DataDeletionRequest.js'
import { ValidationError, ConflictError } from '../../server/errors/index.js'

// Create a mock interface that matches ProcessDataDeletionRequest
interface MockProcessDataDeletionRequest {
  createRequest: ReturnType<typeof jest.fn>
  getRequestStatus: ReturnType<typeof jest.fn>
  getRequestsByEmail: ReturnType<typeof jest.fn>
  processRequest: ReturnType<typeof jest.fn>
  completeRequest: ReturnType<typeof jest.fn>
  cancelRequest: ReturnType<typeof jest.fn>
}

// Mock the ProcessDataDeletionRequest service
const mockProcessDataDeletionRequest: MockProcessDataDeletionRequest = {
  createRequest: jest.fn(),
  getRequestStatus: jest.fn(),
  getRequestsByEmail: jest.fn(),
  processRequest: jest.fn(),
  completeRequest: jest.fn(),
  cancelRequest: jest.fn()
}

describe('DataDeletionController', () => {
  let controller: DataDeletionController

  beforeEach(() => {
    controller = new DataDeletionController(mockProcessDataDeletionRequest as unknown as ProcessDataDeletionRequest)
    jest.clearAllMocks()
  })

  describe('createDeletionRequest', () => {
    it('should create a deletion request successfully', async () => {
      const mockRequest = DataDeletionRequest.create(
        'del_123',
        'user_123',
        '<EMAIL>',
        '127.0.0.1',
        'Mozilla/5.0',
        'no longer needed'
      )

      mockProcessDataDeletionRequest.createRequest.mockResolvedValue(mockRequest)

      const context = {
        body: {
          email: '<EMAIL>',
          reason: 'no longer needed',
          confirmDeletion: true,
          confirmIdentity: true
        },
        headers: { 'user-agent': 'Mozilla/5.0' },
        ip: '127.0.0.1'
      }

      const result = await controller.createDeletionRequest(context)

      expect(result.status).toBe(201)
      expect(result.data.success).toBe(true)
      expect(result.data.requestId).toBe('del_123')
      expect(result.data.email).toBe('<EMAIL>')
      expect(mockProcessDataDeletionRequest.createRequest).toHaveBeenCalledWith({
        email: '<EMAIL>',
        reason: 'no longer needed',
        ipAddress: '127.0.0.1',
        userAgent: 'Mozilla/5.0'
      })
    })

    it('should throw ValidationError when email is missing', async () => {
      const context = {
        body: {
          email: '',
          confirmDeletion: true,
          confirmIdentity: true
        }
      }

      await expect(controller.createDeletionRequest(context)).rejects.toThrow(ValidationError)
    })

    it('should throw ValidationError when confirmDeletion is false', async () => {
      const context = {
        body: {
          email: '<EMAIL>',
          confirmDeletion: false,
          confirmIdentity: true
        }
      }

      await expect(controller.createDeletionRequest(context)).rejects.toThrow(ValidationError)
    })

    it('should throw ValidationError when confirmIdentity is false', async () => {
      const context = {
        body: {
          email: '<EMAIL>',
          confirmDeletion: true,
          confirmIdentity: false
        }
      }

      await expect(controller.createDeletionRequest(context)).rejects.toThrow(ValidationError)
    })

    it('should handle ConflictError when request already exists', async () => {
      mockProcessDataDeletionRequest.createRequest.mockRejectedValue(
        new ConflictError('A pending deletion request already exists for this email')
      )

      const context = {
        body: {
          email: '<EMAIL>',
          confirmDeletion: true,
          confirmIdentity: true
        }
      }

      await expect(controller.createDeletionRequest(context)).rejects.toThrow(ConflictError)
    })
  })

  describe('getRequestStatus', () => {
    it('should return request status successfully', async () => {
      const mockRequest = DataDeletionRequest.create(
        'del_123',
        'user_123',
        '<EMAIL>'
      )

      mockProcessDataDeletionRequest.getRequestStatus.mockResolvedValue(mockRequest)

      const result = await controller.getRequestStatus('del_123')

      expect(result.status).toBe(200)
      expect(result.data.requestId).toBe('del_123')
      expect(result.data.email).toBe('<EMAIL>')
      expect(result.data.status).toBe('pending')
    })

    it('should throw ValidationError when requestId is missing', async () => {
      await expect(controller.getRequestStatus('')).rejects.toThrow(ValidationError)
    })
  })

  describe('getRequestsByEmail', () => {
    it('should return requests by email successfully', async () => {
      const mockRequests = [
        DataDeletionRequest.create('del_123', 'user_123', '<EMAIL>'),
        DataDeletionRequest.create('del_456', 'user_123', '<EMAIL>')
      ]

      mockProcessDataDeletionRequest.getRequestsByEmail.mockResolvedValue(mockRequests)

      const result = await controller.getRequestsByEmail('<EMAIL>')

      expect(result.status).toBe(200)
      expect(result.data.email).toBe('<EMAIL>')
      expect(result.data.requests).toHaveLength(2)
      expect(result.data.requests[0].requestId).toBe('del_123')
      expect(result.data.requests[1].requestId).toBe('del_456')
    })

    it('should throw ValidationError when email is missing', async () => {
      await expect(controller.getRequestsByEmail('')).rejects.toThrow(ValidationError)
    })
  })

  describe('processRequest', () => {
    it('should process request successfully', async () => {
      const mockRequest = DataDeletionRequest.create(
        'del_123',
        'user_123',
        '<EMAIL>'
      ).updateStatus('processing', 'Admin processing')

      mockProcessDataDeletionRequest.processRequest.mockResolvedValue(mockRequest)

      const result = await controller.processRequest('del_123', 'Admin processing')

      expect(result.status).toBe(200)
      expect(result.data.success).toBe(true)
      expect(result.data.requestId).toBe('del_123')
      expect(result.data.status).toBe('processing')
    })
  })

  describe('completeRequest', () => {
    it('should complete request successfully', async () => {
      const mockRequest = DataDeletionRequest.create(
        'del_123',
        'user_123',
        '<EMAIL>'
      ).updateStatus('completed', 'Data deletion completed')

      mockProcessDataDeletionRequest.completeRequest.mockResolvedValue(mockRequest)

      const result = await controller.completeRequest('del_123', 'Data deletion completed')

      expect(result.status).toBe(200)
      expect(result.data.success).toBe(true)
      expect(result.data.requestId).toBe('del_123')
      expect(result.data.status).toBe('completed')
    })
  })

  describe('cancelRequest', () => {
    it('should cancel request successfully', async () => {
      const mockRequest = DataDeletionRequest.create(
        'del_123',
        'user_123',
        '<EMAIL>'
      ).updateStatus('cancelled', 'Cancelled by admin')

      mockProcessDataDeletionRequest.cancelRequest.mockResolvedValue(mockRequest)

      const result = await controller.cancelRequest('del_123', 'Cancelled by admin')

      expect(result.status).toBe(200)
      expect(result.data.success).toBe(true)
      expect(result.data.requestId).toBe('del_123')
      expect(result.data.status).toBe('cancelled')
    })
  })
})
