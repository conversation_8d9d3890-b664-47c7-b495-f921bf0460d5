import { describe, it, expect, beforeEach } from 'bun:test'
import { GetDataDeletionForm } from '../application/GetDataDeletionForm.js'

describe('GetDataDeletionForm', () => {
  let getDataDeletionForm: GetDataDeletionForm

  beforeEach(() => {
    getDataDeletionForm = new GetDataDeletionForm()
  })

  describe('execute', () => {
    it('should generate data deletion form HTML', () => {
      const result = getDataDeletionForm.execute()

      expect(result).toBeDefined()
      expect(result.id).toBe('data-deletion-form')
      expect(result.type).toBe('data-deletion')
      expect(result.title).toBe('BuscaFarma - Solicitud de Eliminación de Cuenta')

      const html = result.toHTML()
      expect(html).toContain('BuscaFarma')
      expect(html).toContain('Solicitud de Eliminación de Cuenta')
      expect(html).toContain('form')
      expect(html).toContain('email')
    })
  })

  describe('executeStandalone', () => {
    it('should generate standalone data deletion page with full HTML structure', () => {
      const result = getDataDeletionForm.executeStandalone()

      expect(result).toBeDefined()
      expect(result.id).toBe('data-deletion-standalone')
      expect(result.type).toBe('data-deletion')
      expect(result.title).toBe('BuscaFarma - Solicitud de Eliminación de Cuenta')

      const html = result.toHTML()
      
      // Check for full HTML document structure
      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('<html lang="es">')
      expect(html).toContain('<head>')
      expect(html).toContain('<body>')
      expect(html).toContain('</html>')
    })

    it('should include proper meta tags and SEO information', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check meta tags
      expect(html).toContain('<meta charset="UTF-8">')
      expect(html).toContain('<meta name="viewport"')
      expect(html).toContain('<meta name="description"')
      expect(html).toContain('<meta name="keywords"')
      expect(html).toContain('<meta name="author" content="BuscaFarma">')

      // Check Open Graph tags
      expect(html).toContain('<meta property="og:title"')
      expect(html).toContain('<meta property="og:description"')
      expect(html).toContain('<meta property="og:type" content="website"')
      expect(html).toContain('<meta property="og:url"')

      // Check title
      expect(html).toContain('<title>BuscaFarma - Solicitud de Eliminación de Cuenta</title>')
    })

    it('should include comprehensive BuscaFarma branding', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check for BuscaFarma branding elements
      expect(html).toContain('🏥 BuscaFarma')
      expect(html).toContain('Encuentra tu farmacia más cercana')
      expect(html).toContain('© 2024 BuscaFarma')
      
      // Check for app description
      expect(html).toContain('aplicación móvil')
      expect(html).toContain('farmacias cercanas')
      expect(html).toContain('medicamentos favoritos')
      
      // Check disclaimer
      expect(html).toContain('No estamos afiliados con entidades gubernamentales')
    })

    it('should include complete form with all required fields', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check form structure
      expect(html).toContain('<form id="deletionRequestForm"')
      expect(html).toContain('class="deletion-form"')

      // Check email field
      expect(html).toContain('type="email"')
      expect(html).toContain('id="email"')
      expect(html).toContain('name="email"')
      expect(html).toContain('required')
      expect(html).toContain('placeholder="<EMAIL>"')

      // Check reason field
      expect(html).toContain('id="reason"')
      expect(html).toContain('name="reason"')
      expect(html).toContain('<select')

      // Check confirmation checkboxes
      expect(html).toContain('id="confirmDeletion"')
      expect(html).toContain('name="confirmDeletion"')
      expect(html).toContain('id="confirmIdentity"')
      expect(html).toContain('name="confirmIdentity"')
      expect(html).toContain('type="checkbox"')

      // Check submit button
      expect(html).toContain('type="submit"')
      expect(html).toContain('🗑️ Solicitar Eliminación de Cuenta')
    })

    it('should include comprehensive data retention information', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check data retention table
      expect(html).toContain('Información sobre Retención de Datos')
      expect(html).toContain('<table>')
      expect(html).toContain('<thead>')
      expect(html).toContain('<tbody>')

      // Check data types that will be deleted
      expect(html).toContain('Información Personal')
      expect(html).toContain('Datos de Ubicación')
      expect(html).toContain('Datos de Salud y Preferencias')
      expect(html).toContain('Medicamentos Favoritos')
      expect(html).toContain('Tokens de Autenticación')

      // Check data types that will be retained
      expect(html).toContain('Registros de Auditoría')
      expect(html).toContain('Registro de Solicitud de Eliminación')

      // Check retention periods
      expect(html).toContain('Inmediato')
      expect(html).toContain('7 años')

      // Check status badges
      expect(html).toContain('✅ Sí')
      expect(html).toContain('❌ No')
    })

    it('should include step-by-step instructions', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      expect(html).toContain('Instrucciones Paso a Paso')
      expect(html).toContain('<ol class="instructions-list">')
      expect(html).toContain('Completa el formulario')
      expect(html).toContain('Revisión de datos')
      expect(html).toContain('Confirmación')
      expect(html).toContain('Procesamiento')
      expect(html).toContain('Notificación')
    })

    it('should include contact and support information', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      expect(html).toContain('Soporte y Contacto')
      expect(html).toContain('<EMAIL>')
      expect(html).toContain('24-48 horas')
      expect(html).toContain('30 días')
      expect(html).toContain('www.buscafarma.com')
      expect(html).toContain('problemas para acceder')
    })

    it('should include legal information and compliance details', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      expect(html).toContain('Información Legal')
      expect(html).toContain('GDPR')
      expect(html).toContain('Google Play Store')
      expect(html).toContain('regulaciones de protección de datos')
      expect(html).toContain('prevención de fraude')
      expect(html).toContain('/privacy-policy.html')
    })

    it('should include comprehensive CSS styling', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check for CSS styles
      expect(html).toContain('<style>')
      expect(html).toContain('font-family:')
      expect(html).toContain('background:')
      expect(html).toContain('border-radius:')
      expect(html).toContain('linear-gradient')

      // Check for responsive design
      expect(html).toContain('@media (max-width: 768px)')
      expect(html).toContain('@media (max-width: 480px)')

      // Check for interactive styles
      expect(html).toContain(':hover')
      expect(html).toContain(':focus')
      expect(html).toContain('transition:')
    })

    it('should include JavaScript functionality', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check for JavaScript
      expect(html).toContain('<script>')
      expect(html).toContain('addEventListener')
      expect(html).toContain('preventDefault')
      expect(html).toContain('fetch(\'/data-deletion/request\'')

      // Check for form validation
      expect(html).toContain('checkValidity')
      expect(html).toContain('FormData')

      // Check for error handling
      expect(html).toContain('successMessage')
      expect(html).toContain('errorMessage')
      expect(html).toContain('catch (error)')

      // Check for loading states
      expect(html).toContain('disabled = true')
      expect(html).toContain('⏳ Procesando...')
    })

    it('should include proper accessibility features', () => {
      const result = getDataDeletionForm.executeStandalone()
      const html = result.toHTML()

      // Check for proper labels
      expect(html).toContain('<label for="email">')
      expect(html).toContain('<label for="reason">')

      // Check for form help text
      expect(html).toContain('form-help')
      expect(html).toContain('<small')

      // Check for semantic HTML structure
      expect(html).toContain('<div class="container">')
      expect(html).toContain('<div class="content-section">')
      expect(html).toContain('<h1>')
      expect(html).toContain('<h2>')
      expect(html).toContain('<h3>')
    })
  })
})
