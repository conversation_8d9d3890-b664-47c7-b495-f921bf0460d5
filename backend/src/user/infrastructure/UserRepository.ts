import { Prisma, PrismaClient, user as PrismaUser } from '@prisma/client'
import type { IUser } from '../domain/IUser.js'
import { HealthData, Point, User, UserPreferences } from '../domain/User.js'

export class UserRepository implements IUser {
  private readonly db: PrismaClient

  constructor() {
    this.db = new PrismaClient()
  }

  private mapToUser(dbUser: PrismaUser): User {
    return new User(
      dbUser.id,
      dbUser.name,
      dbUser.email,
      dbUser.firebase_token,
      dbUser.current_location ? (dbUser.current_location as unknown as Point) : undefined,
      dbUser.preferences as unknown as UserPreferences,
      dbUser.health_data as unknown as HealthData,
      dbUser.profile_picture_url ?? undefined,
      dbUser.refreshToken ?? undefined
    )
  }

  async create(id: string, name: string, email: string, profilePictureUrl: string, firebaseToken: string): Promise<User> {
    const defaultPreferences = {
      language: 'es',
      theme: 'light',
      notifications: true
    } satisfies UserPreferences

    const defaultHealthData = {
      allergies: [],
      conditions: [],
      medications: []
    } satisfies HealthData

    const user = await this.db.user.create({
      data: {
        id,
        name,
        email,
        firebase_token: firebaseToken,
        preferences: defaultPreferences as unknown as Prisma.JsonObject,
        health_data: defaultHealthData as unknown as Prisma.JsonObject,
        profile_picture_url: profilePictureUrl
      }
    })

    return this.mapToUser(user)
  }

  async findById(id: string): Promise<User | null> {
    const user = await this.db.user.findUnique({
      where: { id }
    })

    if (!user) return null
    return this.mapToUser(user)
  }

  async findByEmail(email: string): Promise<User | null> {
    const user = await this.db.user.findUnique({
      where: { email }
    })

    if (!user) return null
    return this.mapToUser(user)
  }

  async updatePreferences(id: string, preferences: Partial<UserPreferences>): Promise<User> {
    const user = await this.db.user.findUnique({ where: { id } })
    if (!user) throw new Error('User not found')

    const updatedPreferences = {
      ...(user.preferences as unknown as UserPreferences),
      ...preferences
    }

    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        preferences: updatedPreferences as unknown as Prisma.JsonObject
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateHealthData(id: string, healthData: Partial<HealthData>): Promise<User> {
    const user = await this.db.user.findUnique({ where: { id } })
    if (!user) throw new Error('User not found')

    const updatedHealthData = {
      ...(user.health_data as unknown as HealthData),
      ...healthData
    }

    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        health_data: updatedHealthData as unknown as Prisma.JsonObject
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateLocation(id: string, location: Point): Promise<User> {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        current_location: location as unknown as Prisma.JsonObject
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateProfilePicture(id: string, url: string): Promise<User> {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        profile_picture_url: url
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateFirebaseToken(id: string, token: string): Promise<User> {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        firebase_token: token
      }
    })

    return this.mapToUser(updatedUser)
  }

  async updateRefreshToken(id: string, token: string): Promise<User> {
    const updatedUser = await this.db.user.update({
      where: { id },
      data: {
        refreshToken: token
      }
    })

    return this.mapToUser(updatedUser)
  }

  async findOrCreateFromFirebase(
    firebaseId: string,
    email: string,
    name: string,
    profilePictureUrl: string,
    firebaseToken: string
  ): Promise<{ user: User; isNewUser: boolean }> {
    let user = await this.findById(firebaseId)
    let isNewUser = false

    if (!user) {
      user = await this.create(firebaseId, name, email, profilePictureUrl, firebaseToken)
      isNewUser = true
    } else {
      user = await this.updateFirebaseToken(firebaseId, firebaseToken)
    }

    return { user, isNewUser }
  }
}
