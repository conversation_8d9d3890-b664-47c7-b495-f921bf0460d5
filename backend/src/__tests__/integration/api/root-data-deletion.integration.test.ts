import { describe, it, expect, beforeAll, afterAll } from 'bun:test'

describe('Root Data Deletion Page Integration Tests', () => {
  const baseUrl = 'http://localhost:3000'

  beforeAll(async () => {
    // Set up test environment
    process.env.NODE_ENV = 'test'
  })

  afterAll(async () => {
    // Clean up
  })

  describe('GET /', () => {
    it('should serve the standalone data deletion page', async () => {
      const response = await fetch(`${baseUrl}/`)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')
      
      const html = await response.text()
      
      // Check for essential HTML structure
      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('<html lang="es">')
      expect(html).toContain('BuscaFarma')
      expect(html).toContain('Solicitud de Eliminación de Cuenta')
      
      // Check for form elements
      expect(html).toContain('<form id="deletionRequestForm"')
      expect(html).toContain('type="email"')
      expect(html).toContain('name="email"')
      expect(html).toContain('confirmDeletion')
      expect(html).toContain('confirmIdentity')
      
      // Check for data retention information
      expect(html).toContain('Información sobre Retención de Datos')
      expect(html).toContain('Información Personal')
      expect(html).toContain('Datos de Ubicación')
      expect(html).toContain('Medicamentos Favoritos')
      expect(html).toContain('Registros de Auditoría')
      
      // Check for contact information
      expect(html).toContain('<EMAIL>')
      expect(html).toContain('Soporte y Contacto')
      
      // Check for legal information
      expect(html).toContain('Información Legal')
      expect(html).toContain('GDPR')
      expect(html).toContain('Google Play Store')
      
      // Check for JavaScript functionality
      expect(html).toContain('/data-deletion/request')
      expect(html).toContain('addEventListener')
      
      // Check for responsive design
      expect(html).toContain('@media')
      expect(html).toContain('max-width: 768px')
      
      // Check for security headers
      expect(response.headers.get('x-content-type-options')).toBe('nosniff')
      expect(response.headers.get('x-frame-options')).toBe('DENY')
      expect(response.headers.get('x-xss-protection')).toBe('1; mode=block')
    })

    it('should include proper meta tags for SEO and social sharing', async () => {
      const response = await fetch(`${baseUrl}/`)
      const html = await response.text()
      
      // Check meta tags
      expect(html).toContain('<meta name="description"')
      expect(html).toContain('<meta name="keywords"')
      expect(html).toContain('<meta name="author" content="BuscaFarma"')
      
      // Check Open Graph tags
      expect(html).toContain('<meta property="og:title"')
      expect(html).toContain('<meta property="og:description"')
      expect(html).toContain('<meta property="og:type" content="website"')
      expect(html).toContain('<meta property="og:url"')
      
      // Check viewport and charset
      expect(html).toContain('<meta name="viewport"')
      expect(html).toContain('<meta charset="UTF-8"')
    })

    it('should include comprehensive styling and responsive design', async () => {
      const response = await fetch(`${baseUrl}/`)
      const html = await response.text()
      
      // Check for CSS styles
      expect(html).toContain('<style>')
      expect(html).toContain('font-family:')
      expect(html).toContain('background:')
      expect(html).toContain('border-radius:')
      
      // Check for responsive breakpoints
      expect(html).toContain('@media (max-width: 768px)')
      expect(html).toContain('@media (max-width: 480px)')
      
      // Check for interactive elements
      expect(html).toContain('.btn-primary:hover')
      expect(html).toContain('.form-input:focus')
      expect(html).toContain('transition:')
    })

    it('should include proper form validation and JavaScript functionality', async () => {
      const response = await fetch(`${baseUrl}/`)
      const html = await response.text()
      
      // Check for form validation
      expect(html).toContain('required')
      expect(html).toContain('type="email"')
      expect(html).toContain('checkValidity()')
      
      // Check for JavaScript event handlers
      expect(html).toContain('addEventListener(\'submit\'')
      expect(html).toContain('preventDefault()')
      expect(html).toContain('fetch(\'/data-deletion/request\'')
      
      // Check for error handling
      expect(html).toContain('successMessage')
      expect(html).toContain('errorMessage')
      expect(html).toContain('catch (error)')
    })

    it('should be accessible from root path without authentication', async () => {
      // Test that the endpoint doesn't require authentication
      const response = await fetch(`${baseUrl}/`, {
        headers: {
          // No authentication headers
        }
      })
      
      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')
    })

    it('should have proper caching headers', async () => {
      const response = await fetch(`${baseUrl}/`)
      
      // Check caching headers
      expect(response.headers.get('cache-control')).toContain('public')
      expect(response.headers.get('cache-control')).toContain('max-age=1800') // 30 minutes
    })

    it('should include BuscaFarma branding and identification', async () => {
      const response = await fetch(`${baseUrl}/`)
      const html = await response.text()
      
      // Check for clear BuscaFarma identification
      expect(html).toContain('🏥 BuscaFarma')
      expect(html).toContain('Encuentra tu farmacia más cercana')
      expect(html).toContain('© 2024 BuscaFarma')
      
      // Check that it's clearly identified as the BuscaFarma app
      expect(html).toContain('aplicación móvil')
      expect(html).toContain('farmacias cercanas')
      expect(html).toContain('medicamentos favoritos')
      
      // Check disclaimer about government affiliation
      expect(html).toContain('No estamos afiliados con entidades gubernamentales')
    })

    it('should include comprehensive data retention information', async () => {
      const response = await fetch(`${baseUrl}/`)
      const html = await response.text()
      
      // Check for data types that will be deleted
      expect(html).toContain('Información Personal')
      expect(html).toContain('Datos de Ubicación')
      expect(html).toContain('Datos de Salud')
      expect(html).toContain('Medicamentos Favoritos')
      expect(html).toContain('Tokens de Autenticación')
      
      // Check for data types that will be retained
      expect(html).toContain('Registros de Auditoría')
      expect(html).toContain('Registro de Solicitud de Eliminación')
      
      // Check for retention periods
      expect(html).toContain('Inmediato')
      expect(html).toContain('7 años')
      
      // Check for legal reasons
      expect(html).toContain('Cumplimiento legal')
      expect(html).toContain('prevención de fraude')
    })

    it('should include proper contact and support information', async () => {
      const response = await fetch(`${baseUrl}/`)
      const html = await response.text()
      
      // Check for support contact information
      expect(html).toContain('<EMAIL>')
      expect(html).toContain('24-48 horas')
      expect(html).toContain('30 días')
      expect(html).toContain('www.buscafarma.com')
      
      // Check for helpful instructions
      expect(html).toContain('Instrucciones Paso a Paso')
      expect(html).toContain('problemas para acceder')
      expect(html).toContain('contacta nuestro equipo')
    })
  })
})
