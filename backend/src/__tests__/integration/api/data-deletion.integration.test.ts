import { describe, it, expect, beforeAll, afterAll, beforeEach } from 'bun:test'
import { PrismaClient } from '@prisma/client'
import { Server } from '../../../server/server.js'

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'file:./test-data-deletion.db'
    }
  }
})

describe('Data Deletion API Integration Tests', () => {
  let server: Server
  let baseUrl: string

  beforeAll(async () => {
    // Set up test environment
    process.env.NODE_ENV = 'test'
    process.env.DATABASE_URL = 'file:./test-data-deletion.db'
    
    // Initialize server
    server = new Server()
    baseUrl = 'http://localhost:3000'
    
    // Clean up database
    await prisma.data_deletion_request.deleteMany()
    await prisma.user.deleteMany()
  })

  afterAll(async () => {
    // Clean up
    await prisma.data_deletion_request.deleteMany()
    await prisma.user.deleteMany()
    await prisma.$disconnect()
  })

  beforeEach(async () => {
    // Clean up before each test
    await prisma.data_deletion_request.deleteMany()
  })

  describe('GET /data-deletion.html', () => {
    it('should serve the data deletion form', async () => {
      const response = await fetch(`${baseUrl}/data-deletion.html`)
      
      expect(response.status).toBe(200)
      expect(response.headers.get('content-type')).toContain('text/html')
      
      const html = await response.text()
      expect(html).toContain('BuscaFarma')
      expect(html).toContain('Solicitud de Eliminación de Cuenta')
      expect(html).toContain('form')
      expect(html).toContain('email')
    })
  })

  describe('POST /data-deletion/request', () => {
    it('should create a deletion request successfully', async () => {
      const requestData = {
        email: '<EMAIL>',
        reason: 'no longer needed',
        confirmDeletion: true,
        confirmIdentity: true
      }

      const response = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(response.status).toBe(201)
      
      const result = await response.json()
      expect(result.success).toBe(true)
      expect(result.email).toBe('<EMAIL>')
      expect(result.requestId).toBeDefined()
      expect(result.status).toBe('pending')
      expect(result.estimatedCompletion).toBe('Within 30 days')
      expect(result.dataRetentionInfo).toBeDefined()
      expect(Array.isArray(result.dataRetentionInfo)).toBe(true)
    })

    it('should reject request with missing email', async () => {
      const requestData = {
        email: '',
        confirmDeletion: true,
        confirmIdentity: true
      }

      const response = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(response.status).toBe(400)
      
      const result = await response.json()
      expect(result.message).toContain('Email is required')
    })

    it('should reject request without deletion confirmation', async () => {
      const requestData = {
        email: '<EMAIL>',
        confirmDeletion: false,
        confirmIdentity: true
      }

      const response = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(response.status).toBe(400)
      
      const result = await response.json()
      expect(result.message).toContain('confirm that you understand the deletion consequences')
    })

    it('should reject request without identity confirmation', async () => {
      const requestData = {
        email: '<EMAIL>',
        confirmDeletion: true,
        confirmIdentity: false
      }

      const response = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(response.status).toBe(400)
      
      const result = await response.json()
      expect(result.message).toContain('confirm that you are the account owner')
    })

    it('should reject duplicate requests for same email', async () => {
      const requestData = {
        email: '<EMAIL>',
        confirmDeletion: true,
        confirmIdentity: true
      }

      // First request should succeed
      const firstResponse = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(firstResponse.status).toBe(201)

      // Second request should fail
      const secondResponse = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(secondResponse.status).toBe(409)
      
      const result = await secondResponse.json()
      expect(result.message).toContain('pending deletion request already exists')
    })

    it('should handle invalid email format', async () => {
      const requestData = {
        email: 'invalid-email',
        confirmDeletion: true,
        confirmIdentity: true
      }

      const response = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      expect(response.status).toBe(400)
    })
  })

  describe('GET /data-deletion/status/:requestId', () => {
    it('should return request status', async () => {
      // First create a request
      const requestData = {
        email: '<EMAIL>',
        confirmDeletion: true,
        confirmIdentity: true
      }

      const createResponse = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      const createResult = await createResponse.json()
      const requestId = createResult.requestId

      // Then check status
      const statusResponse = await fetch(`${baseUrl}/data-deletion/status/${requestId}`)
      
      expect(statusResponse.status).toBe(200)
      
      const statusResult = await statusResponse.json()
      expect(statusResult.requestId).toBe(requestId)
      expect(statusResult.email).toBe('<EMAIL>')
      expect(statusResult.status).toBe('pending')
      expect(statusResult.requestedAt).toBeDefined()
      expect(statusResult.estimatedCompletion).toBe('Within 30 days')
    })

    it('should return 404 for non-existent request', async () => {
      const response = await fetch(`${baseUrl}/data-deletion/status/non-existent-id`)
      
      expect(response.status).toBe(404)
      
      const result = await response.json()
      expect(result.message).toContain('not found')
    })
  })

  describe('Data retention information', () => {
    it('should include comprehensive data retention info in requests', async () => {
      const requestData = {
        email: '<EMAIL>',
        confirmDeletion: true,
        confirmIdentity: true
      }

      const response = await fetch(`${baseUrl}/data-deletion/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      const result = await response.json()
      const retentionInfo = result.dataRetentionInfo

      expect(retentionInfo).toBeDefined()
      expect(Array.isArray(retentionInfo)).toBe(true)
      expect(retentionInfo.length).toBeGreaterThan(0)

      // Check for specific data types
      const dataTypes = retentionInfo.map((info: any) => info.dataType)
      expect(dataTypes).toContain('Personal Information (name, email)')
      expect(dataTypes).toContain('Location Data')
      expect(dataTypes).toContain('Health Data & Preferences')
      expect(dataTypes).toContain('Medication Favorites')
      expect(dataTypes).toContain('Authentication Tokens')
      expect(dataTypes).toContain('Audit Logs')
      expect(dataTypes).toContain('Deletion Request Record')

      // Check that some data will be deleted and some retained
      const willDelete = retentionInfo.filter((info: any) => info.willBeDeleted)
      const willRetain = retentionInfo.filter((info: any) => !info.willBeDeleted)
      
      expect(willDelete.length).toBeGreaterThan(0)
      expect(willRetain.length).toBeGreaterThan(0)
    })
  })
})
