import { JWT } from '../services/jwt.js'
import { LoginUser } from '../user/application/login.js'
import { UserRepository } from '../user/infrastructure/UserRepository.js'
import { LoginController } from '../user/infrastructure/controllers/login.js'

import { AddMedicationFavorite } from '../favorites/application/AddMedicationFavorite.js'
import { ListMedicationFavorites } from '../favorites/application/ListMedicationFavorites.js'
import { RemoveMedicationFavorite } from '../favorites/application/RemoveMedicationFavorite.js'
import { SearchMedicationFavorites } from '../favorites/application/SearchMedicationFavorites.js'
// Favorites dependencies
import { MedicationFavoriteRepository } from '../favorites/infrastructure/MedicationFavoriteRepository.js'
import { MedicationFavoriteController } from '../favorites/infrastructure/controllers/MedicationFavoriteController.js'

// Legal dependencies
import { GetLegalDocument } from '../legal/application/GetLegalDocument.js'
import { GetDataDeletionForm } from '../legal/application/GetDataDeletionForm.js'
import { ProcessDataDeletionRequest } from '../legal/application/ProcessDataDeletionRequest.js'
import { LegalController } from '../legal/infrastructure/controllers/LegalController.js'
import { DataDeletionController } from '../legal/infrastructure/controllers/DataDeletionController.js'
import { PrismaDataDeletionRepository } from '../legal/infrastructure/repositories/DataDeletionRepository.js'

// Email service
import { EmailService } from '../services/EmailService.js'

// Shared services
const jwtService = new JWT()

// User dependencies
const userRepository = new UserRepository()
const loginUser = new LoginUser(userRepository, jwtService)
export const loginController = new LoginController(loginUser)

// Medication favorites dependencies
const medicationFavoriteRepository = new MedicationFavoriteRepository()
const addMedicationFavorite = new AddMedicationFavorite(medicationFavoriteRepository, jwtService)
const removeMedicationFavorite = new RemoveMedicationFavorite(medicationFavoriteRepository, jwtService)
const listMedicationFavorites = new ListMedicationFavorites(medicationFavoriteRepository, jwtService)
const searchMedicationFavorites = new SearchMedicationFavorites(medicationFavoriteRepository, jwtService)
export const medicationFavoriteController = new MedicationFavoriteController(
  addMedicationFavorite,
  removeMedicationFavorite,
  listMedicationFavorites,
  searchMedicationFavorites
)

// Legal dependencies
const getLegalDocument = new GetLegalDocument()
const getDataDeletionForm = new GetDataDeletionForm()

// Gmail SMTP Email service configuration
// For Gmail setup instructions, see EmailService.ts documentation
const emailConfig = {
  host: process.env.EMAIL_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.EMAIL_PORT || '587'),
  secure: process.env.EMAIL_SECURE === 'true', // false for 587, true for 465
  auth: {
    user: process.env.EMAIL_USER || '', // Gmail address
    pass: process.env.EMAIL_PASS || ''  // Gmail App Password (16 characters)
  },
  from: process.env.EMAIL_FROM || process.env.EMAIL_USER || '<EMAIL>',
  service: 'gmail', // Explicitly set Gmail service
  tls: {
    rejectUnauthorized: false
  }
}
const emailService = new EmailService(emailConfig)

// Data deletion dependencies
const dataDeletionRepository = new PrismaDataDeletionRepository(userRepository.prisma)
const processDataDeletionRequest = new ProcessDataDeletionRequest(dataDeletionRepository, emailService)

export const legalController = new LegalController(getLegalDocument, getDataDeletionForm)
export const dataDeletionController = new DataDeletionController(processDataDeletionRequest)
