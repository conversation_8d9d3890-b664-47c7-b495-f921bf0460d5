# Gmail SMTP Configuration for BuscaFarma Backend

This guide explains how to configure Gmail SMTP for sending emails in the BuscaFarma backend application, specifically for the data deletion notification system.

## Overview

The BuscaFarma backend uses Gmail SMTP to send:
- Data deletion request confirmation emails
- Data deletion completion notifications
- Admin notifications for new deletion requests

## Prerequisites

1. **Gmail Account**: You need a Gmail account to send emails
2. **2-Factor Authentication**: Must be enabled on your Gmail account
3. **App Password**: Required for secure authentication

## Step-by-Step Setup

### 1. Enable 2-Factor Authentication

1. Go to [Google Account Security](https://myaccount.google.com/security)
2. Under "Signing in to Google", click on "2-Step Verification"
3. Follow the prompts to enable 2FA using your phone number
4. Verify that 2-Step Verification is turned on

### 2. Generate Gmail App Password

1. Go to [Google App Passwords](https://myaccount.google.com/apppasswords)
2. You may need to sign in again
3. Under "Select app", choose "Mail"
4. Under "Select device", choose "Other (Custom name)"
5. Enter "BuscaFarma Backend" as the custom name
6. Click "Generate"
7. **Important**: Copy the 16-character password immediately (you won't see it again)

### 3. Configure Environment Variables

Create a `.env` file in the backend directory with the following configuration:

```bash
# Gmail SMTP Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-16-character-app-password
EMAIL_FROM=<EMAIL>

# Optional: Admin email for notifications
ADMIN_EMAIL=<EMAIL>
```

**Replace the following:**
- `<EMAIL>`: Your actual Gmail address
- `your-16-character-app-password`: The app password from step 2

### 4. Test the Configuration

1. Start the BuscaFarma backend server:
   ```bash
   bun run dev
   ```

2. Check the console output for email service verification:
   ```
   ✅ Email service connected successfully to Gmail SMTP
   ```

3. Test by creating a data deletion request:
   ```bash
   curl -X POST http://localhost:3000/data-deletion/request \
     -H "Content-Type: application/json" \
     -d '{
       "email": "<EMAIL>",
       "confirmDeletion": true,
       "confirmIdentity": true
     }'
   ```

4. Check your Gmail "Sent" folder for the confirmation email

## Configuration Options

### SMTP Settings

| Setting | Value | Description |
|---------|-------|-------------|
| `EMAIL_HOST` | `smtp.gmail.com` | Gmail SMTP server |
| `EMAIL_PORT` | `587` (TLS) or `465` (SSL) | SMTP port |
| `EMAIL_SECURE` | `false` for 587, `true` for 465 | Security mode |

### Recommended Settings

For maximum compatibility, use:
- **Port 587** with **TLS** (`EMAIL_SECURE=false`)
- This is Gmail's recommended configuration

## Gmail Limitations

### Daily Sending Limits

- **Gmail Free**: ~500 emails per day
- **Google Workspace**: ~2,000 emails per day

### Rate Limiting

Gmail may temporarily block sending if you exceed rate limits. The system includes automatic retry logic and proper error handling.

## Troubleshooting

### Common Errors and Solutions

#### "Invalid login" or "Username and Password not accepted"

**Cause**: Incorrect credentials or using regular password instead of App Password

**Solution**:
1. Verify your Gmail address is correct
2. Ensure you're using the 16-character App Password, not your regular Gmail password
3. Regenerate the App Password if needed

#### "Daily sending quota exceeded"

**Cause**: Exceeded Gmail's daily sending limit

**Solution**:
1. Wait 24 hours for the quota to reset
2. Consider upgrading to Google Workspace for higher limits
3. Implement email queuing for high-volume applications

#### "Connection refused" or "Timeout"

**Cause**: Network connectivity or firewall issues

**Solution**:
1. Check your internet connection
2. Verify firewall settings allow SMTP traffic on port 587/465
3. Try switching between port 587 and 465

#### "Message rejected" or "Spam detection"

**Cause**: Gmail's spam filters blocking the email

**Solution**:
1. Review email content for spam-like characteristics
2. Ensure recipient email addresses are valid
3. Add proper email headers and formatting

### Debug Mode

To enable detailed logging, set the environment to development:

```bash
NODE_ENV=development
```

In development mode, emails are logged to the console instead of being sent, allowing you to test the system without actually sending emails.

## Security Best Practices

1. **Never commit credentials**: Keep your `.env` file out of version control
2. **Use App Passwords**: Never use your regular Gmail password
3. **Monitor activity**: Regularly check Gmail security settings for suspicious activity
4. **Rotate passwords**: Regenerate App Passwords periodically
5. **Limit access**: Only provide email credentials to necessary team members

## Production Deployment

### Environment Variables

Set the following environment variables in your production environment:

```bash
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=<EMAIL>
EMAIL_PASS=production-app-password
EMAIL_FROM=<EMAIL>
ADMIN_EMAIL=<EMAIL>
NODE_ENV=production
```

### Monitoring

The system automatically logs email sending status and provides detailed error messages. Monitor your application logs for:

- Email service connection status on startup
- Successful email sending confirmations
- Error messages with troubleshooting hints

### Scaling Considerations

For high-volume email sending, consider:

1. **Google Workspace**: Higher sending limits
2. **Email queuing**: Implement Redis-based email queues
3. **Multiple accounts**: Distribute load across multiple Gmail accounts
4. **Professional email service**: Consider services like SendGrid, Mailgun, or AWS SES

## Support

If you encounter issues not covered in this guide:

1. Check the application logs for detailed error messages
2. Verify your Gmail account settings and App Password
3. Test with a simple email client to isolate configuration issues
4. Consult the [Gmail SMTP documentation](https://support.google.com/mail/answer/7126229)

## Example .env File

Copy `.env.gmail.example` to `.env` and update with your credentials:

```bash
cp .env.gmail.example .env
# Edit .env with your Gmail credentials
```
