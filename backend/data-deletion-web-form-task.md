# Context
Filename: data-deletion-web-form-task.md
Created On: 2025-01-27
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Implement a data deletion web form in backend mechanism for BuscaFarma app that complies with Google Play Store requirements:

1. Create a dedicated web page or in-app section that:
   - Clearly identifies "BuscaFarma" as the application
   - Provides step-by-step instructions for users to request account and associated data deletion
   - Specifies which user data will be permanently deleted and which data might be retained (with retention periods)
   - Includes contact information for support with deletion requests

2. Add the data deletion request URL to the Google Play Store listing for the BuscaFarma app

3. Ensure the deletion mechanism complies with Google Play's Data Safety section requirements and privacy regulations

4. Document the implementation in the project repository for future reference

The data deletion request process should be user-friendly, transparent about data handling practices, and accessible from both the app and the Google Play Store listing.

# Project Overview
BuscaFarma backend API built with:
- **Runtime:** Bun.js with TypeScript
- **Framework:** Elysia.js with schema validation and Swagger documentation
- **Database:** MongoDB with Prisma ORM (production), SQLite for testing
- **Authentication:** JWT tokens with Firebase integration
- **Architecture:** Clean architecture with Domain/Application/Infrastructure layers

**Current API Structure:**
- Main server at `/api/v1` prefix with user and favorites routers
- User authentication and management (`/api/v1/users/*`)
- Medication favorites system (`/api/v1/favorites/*`)
- Legal documents system (`/privacy-policy.html`, `/privacy-policy.txt`)
- Swagger documentation at `/swagger`

**Existing Legal Module:**
- Legal domain model with LegalDocument entity
- Legal application service for document retrieval
- Legal controller with HTML/plain text responses
- Legal router mounted at root level (public access)
- Privacy policy endpoint already implemented

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

**Google Play Store Requirements Analysis:**

**Key Requirements from Google Play Policy:**
1. **In-app deletion path:** Users must be able to initiate account deletion from within the app
2. **Web resource requirement:** Must provide a web link where users can request account deletion without requiring app re-download
3. **Data transparency:** Must clearly specify what data will be deleted and what might be retained
4. **Contact information:** Must provide support contact for deletion requests
5. **Functional web link:** Must load without error, be relevant in scope, and reference the app/developer name
6. **Data Safety form:** Must complete Data deletion questions in Play Console

**Current BuscaFarma User Data Analysis:**
Based on User domain model and Prisma schema:
- **Personal Information:** id, name, email, firebase_token
- **Location Data:** current_location (Point with latitude/longitude)
- **Preferences:** language, theme, notifications, defaultLocation
- **Health Data:** allergies, conditions, medications
- **Profile Data:** profile_picture_url
- **Authentication:** refreshToken, accessToken
- **Favorites:** medication_favorites with complete medication data from MINSA API

**Current Architecture Analysis:**
- **Legal Module Exists:** Complete legal module with document management
- **Public Endpoints:** Legal router already mounted at root level without authentication
- **HTML Generation:** LegalDocument class supports HTML and plain text output
- **Existing Pattern:** Privacy policy endpoint at `/privacy-policy.html` already implemented
- **Router Structure:** Legal router uses Elysia without authentication derive

**Technical Implementation Requirements:**
1. **New Legal Document Type:** Add 'data-deletion' to LegalDocumentType
2. **Data Deletion Controller Method:** Add method to handle data deletion requests
3. **Web Form Endpoint:** Create `/data-deletion.html` endpoint with interactive form
4. **Form Processing Endpoint:** Create POST endpoint to handle deletion requests
5. **Email Integration:** Add email service for deletion request notifications
6. **Data Retention Policy:** Document what data is retained and for how long
7. **User Identification:** Secure method to verify user identity for deletion requests

**Security Considerations:**
- **User Verification:** Must verify user identity before processing deletion
- **CSRF Protection:** Form must include CSRF protection
- **Rate Limiting:** Prevent abuse of deletion request system
- **Audit Trail:** Log deletion requests for compliance
- **Data Retention:** Some data may need to be retained for legal/regulatory compliance

**Compliance Requirements:**
- **GDPR Compliance:** Right to erasure (Article 17)
- **Google Play Policy:** Account deletion requirements
- **Data Safety:** Transparency about data handling
- **Legal Retention:** Some data may be required to be retained for fraud prevention, security, or regulatory compliance

