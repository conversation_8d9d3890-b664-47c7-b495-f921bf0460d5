# BuscaFarma Backend - Gmail SMTP Configuration Example
# Copy this file to .env and fill in your actual Gmail credentials

# =============================================================================
# GMAIL SMTP EMAIL CONFIGURATION
# =============================================================================

# Gmail SMTP Server Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_SECURE=false

# Gmail Authentication
# IMPORTANT: Use your Gmail address and App Password (NOT your regular password)
EMAIL_USER=<EMAIL>
EMAIL_PASS="xxiq pgoi xkwe atey"

# From Address (usually same as EMAIL_USER)
EMAIL_FROM=<EMAIL>

# Admin Email for Notifications (optional)
ADMIN_EMAIL=<EMAIL>

# =============================================================================
# HOW TO SET UP GMAIL APP PASSWORD
# =============================================================================
#
# 1. Enable 2-Factor Authentication on your Gmail account:
#    - Go to https://myaccount.google.com/security
#    - Turn on 2-Step Verification
#
# 2. Generate an App Password:
#    - Go to https://myaccount.google.com/apppasswords
#    - Select "Mail" as the app
#    - Select "Other (Custom name)" as the device
#    - Enter "BuscaFarma Backend" as the name
#    - Click "Generate"
#    - Copy the 16-character password (spaces will be removed automatically)
#
# 3. Use the App Password:
#    - Set EMAIL_PASS to the 16-character app password
#    - Do NOT use your regular Gmail password
#
# =============================================================================
# GMAIL SMTP SETTINGS EXPLAINED
# =============================================================================
#
# EMAIL_HOST: Gmail's SMTP server address
# - Always use: smtp.gmail.com
#
# EMAIL_PORT: SMTP port number
# - Use 587 for TLS (recommended)
# - Use 465 for SSL (alternative)
#
# EMAIL_SECURE: SSL/TLS security setting
# - Set to false when using port 587 (TLS)
# - Set to true when using port 465 (SSL)
#
# EMAIL_USER: Your Gmail address
# - Must be a valid Gmail account with 2FA enabled
# - This will be the "from" address for emails
#
# EMAIL_PASS: Gmail App Password
# - 16-character password generated by Google
# - NOT your regular Gmail password
# - Required when 2FA is enabled (recommended)
#
# =============================================================================
# GMAIL LIMITATIONS AND CONSIDERATIONS
# =============================================================================
#
# Daily Sending Limits:
# - Gmail free accounts: ~500 emails per day
# - Google Workspace accounts: ~2000 emails per day
#
# Rate Limiting:
# - Gmail may temporarily block sending if limits are exceeded
# - Implement proper retry logic in production
#
# Security:
# - Always use App Passwords instead of regular passwords
# - Enable 2-Factor Authentication for better security
# - Monitor for suspicious activity in Gmail security settings
#
# Troubleshooting:
# - "Invalid login" error: Check App Password and 2FA settings
# - "Daily quota exceeded": Wait 24 hours or upgrade to Google Workspace
# - Connection timeout: Check firewall and network settings
#
# =============================================================================
# OTHER ENVIRONMENT VARIABLES (for reference)
# =============================================================================

# Database Configuration
DATABASE_URL=mongodb://localhost:27017/buscafarma

# JWT Configuration
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-jwt-refresh-secret-key

# Application Environment
NODE_ENV=development
PORT=3000

# =============================================================================
# TESTING EMAIL CONFIGURATION
# =============================================================================
#
# To test your Gmail configuration:
#
# 1. Set up your .env file with the Gmail settings above
# 2. Start the BuscaFarma backend server
# 3. Check the console for email service connection status
# 4. Test by creating a data deletion request via the API
# 5. Check your Gmail "Sent" folder for confirmation emails
#
# The EmailService will automatically verify the connection on startup
# and provide helpful error messages if configuration is incorrect.
#
# =============================================================================
